import unittest
from unittest.mock import MagicMock, patch
from datetime import datetime
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
from push_notification_processor import PushNotificationProcessor, GoogleSheetsClient

class TestPushNotificationProcessor(unittest.TestCase):
    def setUp(self):
        self.mock_sheets_client = MagicMock(spec=GoogleSheetsClient)

    @patch('push_notification_processor.CurrencyConverter.fetch_usd_to_cop_rate', return_value=4000.0)
    @patch('push_notification_processor.ExpenseInfo')
    def test_process_push_notification_with_cop(self, mock_expense_info, mock_exchange_rate):
        mock_parsed_info = MagicMock()
        mock_parsed_info.currency = "COP"
        mock_parsed_info.amount = "120000"
        mock_parsed_info.place = "Supermercado XYZ"
        mock_expense_info.parse.return_value = mock_parsed_info

        processor = PushNotificationProcessor(self.mock_sheets_client)
        processor.process_push_notification(app_name="any", title="any", description="any")

        self.mock_sheets_client.append_row.assert_called_once()
        appended_row = self.mock_sheets_client.append_row.call_args[0][0]
        self.assertEqual(len(appended_row), 8)       # Now includes categorization columns
        self.assertEqual(appended_row[2], 120000.0)  # COP value as float
        self.assertEqual(appended_row[3], 30.0)      # USD value as float (120000 / 4000)
        self.assertEqual(appended_row[4], "any")     # App name
        self.assertEqual(appended_row[6], "Gastos Esenciales")  # Main category
        self.assertEqual(appended_row[7], "Mercado")            # Subcategory

    @patch('push_notification_processor.CurrencyConverter.fetch_usd_to_cop_rate', return_value=4000.0)
    @patch('push_notification_processor.ExpenseInfo')
    def test_process_push_notification_with_usd(self, mock_expense_info, mock_exchange_rate):
        mock_parsed_info = MagicMock()
        mock_parsed_info.currency = "USD"
        mock_parsed_info.amount = "100"
        mock_parsed_info.place = "Coffee Shop"
        mock_expense_info.parse.return_value = mock_parsed_info

        processor = PushNotificationProcessor(self.mock_sheets_client)
        processor.process_push_notification(app_name="BancoABC", title="any", description="any")

        self.mock_sheets_client.append_row.assert_called_once()
        appended_row = self.mock_sheets_client.append_row.call_args[0][0]
        self.assertEqual(len(appended_row), 8)        # Now includes categorization columns
        self.assertEqual(appended_row[2], 400000.0)   # COP value as float (100 * 4000)
        self.assertEqual(appended_row[3], 100.0)      # USD value as float
        self.assertEqual(appended_row[4], "BancoABC") # App name
        self.assertEqual(appended_row[6], "Gastos Discrecionales")  # Main category
        self.assertEqual(appended_row[7], "Otros")                  # Subcategory

    @patch('push_notification_processor.ExpenseInfo')
    def test_process_push_notification_invalid_text(self, mock_expense_info):
        mock_expense_info.parse.return_value = None

        processor = PushNotificationProcessor(self.mock_sheets_client)
        processor.process_push_notification(app_name="any", title="any", description="any")

        self.mock_sheets_client.append_row.assert_not_called()

    @patch('push_notification_processor.CurrencyConverter.fetch_usd_to_cop_rate', return_value=4000.0)
    @patch('push_notification_processor.ExpenseInfo')
    def test_process_push_notification_unknown_currency(self, mock_expense_info, mock_exchange_rate):
        mock_parsed_info = MagicMock()
        mock_parsed_info.currency = "EUR"  # Unsupported currency
        mock_parsed_info.amount = "50"
        mock_parsed_info.place = "General"
        mock_expense_info.parse.return_value = mock_parsed_info

        processor = PushNotificationProcessor(self.mock_sheets_client)
        processor.process_push_notification(app_name="any", title="any", description="any")

        self.mock_sheets_client.append_row.assert_called_once()
        appended_row = self.mock_sheets_client.append_row.call_args[0][0]
        self.assertEqual(len(appended_row), 8)        # Now includes categorization columns
        self.assertEqual(appended_row[2], 50.0)       # COP value as float
        self.assertAlmostEqual(appended_row[3], 0.0125, places=4)  # USD equivalent (50 / 4000)
        self.assertEqual(appended_row[4], "any")      # App name
        self.assertEqual(appended_row[6], "Gastos Discrecionales")  # Main category
        self.assertEqual(appended_row[7], "Otros")                  # Subcategory

    @patch('push_notification_processor.CurrencyConverter.fetch_usd_to_cop_rate', return_value=4000.0)
    @patch('push_notification_processor.ExpenseInfo')
    def test_categorization_integration(self, mock_expense_info, mock_exchange_rate):
        """Test that categorization works correctly with different expense types"""
        test_cases = [
            # (place, expected_main_category, expected_subcategory)
            ("Rappi Supermercado", "Gastos Discrecionales", "Domicilio"),  # Rappi is food delivery
            ("BodyTech", "Gastos Esenciales", "Gym"),
            ("Netflix", "Gastos Discrecionales", "Subs DFY"),
            ("Uber", "Gastos Discrecionales", "Uber"),
        ]

        for place, expected_main, expected_sub in test_cases:
            with self.subTest(place=place):
                # Reset mock for each test case
                self.mock_sheets_client.reset_mock()

                mock_parsed_info = MagicMock()
                mock_parsed_info.currency = "COP"
                mock_parsed_info.amount = "50000"
                mock_parsed_info.place = place
                mock_expense_info.parse.return_value = mock_parsed_info

                processor = PushNotificationProcessor(self.mock_sheets_client)
                processor.process_push_notification(
                    app_name="TestApp",
                    title="Test",
                    description=f"Transaction at {place}"
                )

                self.mock_sheets_client.append_row.assert_called_once()
                appended_row = self.mock_sheets_client.append_row.call_args[0][0]

                self.assertEqual(appended_row[6], expected_main, f"Main category mismatch for {place}")
                self.assertEqual(appended_row[7], expected_sub, f"Subcategory mismatch for {place}")

    @patch.dict(os.environ, {'OPENAI_API_KEY': 'test_key'})
    @patch('push_notification_processor.AIExpenseCategorizer')
    @patch('push_notification_processor.CurrencyConverter.fetch_usd_to_cop_rate', return_value=4000.0)
    @patch('push_notification_processor.ExpenseInfo')
    def test_ai_categorization_enabled(self, mock_expense_info, mock_exchange_rate, mock_ai_categorizer):
        """Test that AI categorization is used when available"""
        # Mock AI categorizer
        mock_ai_instance = MagicMock()
        mock_ai_instance.validate_api_key.return_value = True
        mock_ai_instance.categorize_expense.return_value = ("Gastos Discrecionales", "Domicilio", 0.95)
        mock_ai_categorizer.return_value = mock_ai_instance

        # Mock expense info
        mock_parsed_info = MagicMock()
        mock_parsed_info.currency = "COP"
        mock_parsed_info.amount = "45000"
        mock_parsed_info.place = "Rappi"
        mock_expense_info.parse.return_value = mock_parsed_info

        # Create processor with AI enabled
        processor = PushNotificationProcessor(self.mock_sheets_client, use_ai=True)
        processor.process_push_notification(
            app_name="TestApp",
            title="Test",
            description="Rappi delivery order"
        )

        # Verify AI categorizer was called
        mock_ai_instance.categorize_expense.assert_called_once_with("Rappi", "45000", "Test Rappi delivery order")

        # Verify correct categorization in the row
        self.mock_sheets_client.append_row.assert_called_once()
        appended_row = self.mock_sheets_client.append_row.call_args[0][0]
        self.assertEqual(appended_row[6], "Gastos Discrecionales")
        self.assertEqual(appended_row[7], "Domicilio")

    @patch.dict(os.environ, {}, clear=True)  # Clear OPENAI_API_KEY
    @patch('push_notification_processor.CurrencyConverter.fetch_usd_to_cop_rate', return_value=4000.0)
    @patch('push_notification_processor.ExpenseInfo')
    def test_ai_categorization_disabled_no_api_key(self, mock_expense_info, mock_exchange_rate):
        """Test that rule-based categorization is used when no API key is available"""
        # Mock expense info
        mock_parsed_info = MagicMock()
        mock_parsed_info.currency = "COP"
        mock_parsed_info.amount = "45000"
        mock_parsed_info.place = "Rappi"
        mock_expense_info.parse.return_value = mock_parsed_info

        # Create processor with AI enabled but no API key
        processor = PushNotificationProcessor(self.mock_sheets_client, use_ai=True)

        # Should fallback to rule-based categorization
        self.assertIsNone(processor.ai_categorizer)
        self.assertIsNotNone(processor.rule_based_categorizer)

        processor.process_push_notification(
            app_name="TestApp",
            title="Test",
            description="Rappi delivery order"
        )

        # Verify categorization still works (rule-based)
        self.mock_sheets_client.append_row.assert_called_once()
        appended_row = self.mock_sheets_client.append_row.call_args[0][0]
        self.assertEqual(appended_row[6], "Gastos Discrecionales")
        self.assertEqual(appended_row[7], "Domicilio")

    @patch.dict(os.environ, {'OPENAI_API_KEY': 'test_key'})
    @patch('push_notification_processor.AIExpenseCategorizer')
    @patch('push_notification_processor.CurrencyConverter.fetch_usd_to_cop_rate', return_value=4000.0)
    @patch('push_notification_processor.ExpenseInfo')
    def test_ai_categorization_fallback_on_error(self, mock_expense_info, mock_exchange_rate, mock_ai_categorizer):
        """Test fallback to rule-based categorization when AI fails"""
        # Mock AI categorizer that fails
        mock_ai_instance = MagicMock()
        mock_ai_instance.validate_api_key.return_value = True
        mock_ai_instance.categorize_expense.side_effect = Exception("AI API Error")
        mock_ai_categorizer.return_value = mock_ai_instance

        # Mock expense info
        mock_parsed_info = MagicMock()
        mock_parsed_info.currency = "COP"
        mock_parsed_info.amount = "45000"
        mock_parsed_info.place = "Rappi"
        mock_expense_info.parse.return_value = mock_parsed_info

        # Create processor with AI enabled
        processor = PushNotificationProcessor(self.mock_sheets_client, use_ai=True)
        processor.process_push_notification(
            app_name="TestApp",
            title="Test",
            description="Rappi delivery order"
        )

        # Verify AI was attempted but fallback was used
        mock_ai_instance.categorize_expense.assert_called_once()

        # Verify rule-based categorization was used as fallback
        self.mock_sheets_client.append_row.assert_called_once()
        appended_row = self.mock_sheets_client.append_row.call_args[0][0]
        self.assertEqual(appended_row[6], "Gastos Discrecionales")
        self.assertEqual(appended_row[7], "Domicilio")

    @patch('push_notification_processor.CurrencyConverter.fetch_usd_to_cop_rate', return_value=4000.0)
    @patch('push_notification_processor.ExpenseInfo')
    def test_ai_categorization_disabled_by_flag(self, mock_expense_info, mock_exchange_rate):
        """Test that AI categorization can be disabled by flag"""
        # Mock expense info
        mock_parsed_info = MagicMock()
        mock_parsed_info.currency = "COP"
        mock_parsed_info.amount = "45000"
        mock_parsed_info.place = "Rappi"
        mock_expense_info.parse.return_value = mock_parsed_info

        # Create processor with AI explicitly disabled
        processor = PushNotificationProcessor(self.mock_sheets_client, use_ai=False)

        # Should not have AI categorizer
        self.assertIsNone(processor.ai_categorizer)
        self.assertIsNotNone(processor.rule_based_categorizer)

        processor.process_push_notification(
            app_name="TestApp",
            title="Test",
            description="Rappi delivery order"
        )

        # Verify rule-based categorization was used
        self.mock_sheets_client.append_row.assert_called_once()
        appended_row = self.mock_sheets_client.append_row.call_args[0][0]
        self.assertEqual(appended_row[6], "Gastos Discrecionales")
        self.assertEqual(appended_row[7], "Domicilio")

if __name__ == '__main__':
    unittest.main()
