from googleapiclient.discovery import build
from google.oauth2.service_account import Credentials

import logging

class GoogleSheetsClient:
    """
    Handles all interactions with the Google Sheets API:
      - Authenticates via the service account JSON.
      - Appends rows to the specified sheet/tab.
    """

    def __init__(self, spreadsheet_id, credentials_file, sheet_range="ALL!A:I"):
        """
        :param spreadsheet_id: The ID of the target Google Spreadsheet.
        :param credentials_file: Path to the service account credentials JSON.
        :param sheet_range: The sheet/tab and columns to append data (e.g. 'ALL!A:I').
                           Now includes columns for Date, Exchange Rate, COP, USD, App, Description,
                           Main Category, and Subcategory.
        """
        self.spreadsheet_id = spreadsheet_id
        self.credentials_file = credentials_file
        self.sheet_range = sheet_range
        self.service = self._init_service()

        # Configure logging (if not configured elsewhere)
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s [%(levelname)s] %(message)s"
        )

    def _init_service(self):
        """
        Builds and returns a Google Sheets service object.
        """
        scopes = ["https://www.googleapis.com/auth/spreadsheets"]
        credentials = Credentials.from_service_account_file(
            self.credentials_file,
            scopes=scopes
        )
        try:
            service = build("sheets", "v4", credentials=credentials)
            logging.info("Google Sheets service initialized successfully.")
            return service
        except Exception as e:
            logging.error(f"Error creating Google Sheets service: {e}")
            raise e

    def append_row(self, row_values):
        """
        Appends a single row to the configured sheet/tab.

        :param row_values: A list representing a single row of data.
        """
        if not isinstance(row_values, list):
            raise ValueError("row_values must be a list.")

        sheet = self.service.spreadsheets()
        body = {"values": [row_values]}  # single row

        try:
            request = sheet.values().append(
                spreadsheetId=self.spreadsheet_id,
                range=self.sheet_range,
                valueInputOption="RAW",
                body=body
            )
            response = request.execute()
            logging.info(f"Data appended to {self.sheet_range}: {response}")
        except Exception as e:
            logging.error(f"Error appending data to the Google Sheet: {e}")

    def create_sheet_tab(self, sheet_name):
        """
        Creates a new sheet tab in the spreadsheet.

        :param sheet_name: Name of the new sheet tab to create.
        """
        try:
            # First check if sheet already exists
            spreadsheet = self.service.spreadsheets().get(
                spreadsheetId=self.spreadsheet_id
            ).execute()

            existing_sheets = [sheet['properties']['title'] for sheet in spreadsheet['sheets']]

            if sheet_name in existing_sheets:
                logging.info(f"Sheet '{sheet_name}' already exists, skipping creation")
                return

            # Create new sheet
            request_body = {
                'requests': [{
                    'addSheet': {
                        'properties': {
                            'title': sheet_name
                        }
                    }
                }]
            }

            response = self.service.spreadsheets().batchUpdate(
                spreadsheetId=self.spreadsheet_id,
                body=request_body
            ).execute()

            logging.info(f"Created new sheet tab: {sheet_name}")

        except Exception as e:
            logging.error(f"Error creating sheet tab '{sheet_name}': {e}")
            raise

    def batch_update_rows(self, data_rows):
        """
        Updates multiple rows at once using batch update.

        :param data_rows: List of lists, where each inner list represents a row.
        """
        if not data_rows:
            logging.warning("No data provided for batch update")
            return

        try:
            body = {"values": data_rows}

            # Clear existing data first
            self.service.spreadsheets().values().clear(
                spreadsheetId=self.spreadsheet_id,
                range=self.sheet_range
            ).execute()

            # Update with new data
            response = self.service.spreadsheets().values().update(
                spreadsheetId=self.spreadsheet_id,
                range=self.sheet_range,
                valueInputOption="RAW",
                body=body
            ).execute()

            logging.info(f"Batch updated {len(data_rows)} rows to {self.sheet_range}")

        except Exception as e:
            logging.error(f"Error in batch update: {e}")
            raise

    def get_sheet_data(self, range_name=None):
        """
        Retrieves data from the sheet.

        :param range_name: Optional specific range, defaults to configured sheet_range.
        :return: List of lists representing the sheet data.
        """
        range_to_use = range_name or self.sheet_range

        try:
            result = self.service.spreadsheets().values().get(
                spreadsheetId=self.spreadsheet_id,
                range=range_to_use
            ).execute()

            values = result.get('values', [])
            logging.info(f"Retrieved {len(values)} rows from {range_to_use}")
            return values

        except Exception as e:
            logging.error(f"Error retrieving data from {range_to_use}: {e}")
            raise

    def apply_column_formatting(self, sheet_name, num_rows):
        """
        Apply comprehensive formatting to all columns in the migrated sheet.

        :param sheet_name: Name of the sheet to format
        :param num_rows: Number of data rows (excluding header)
        """
        try:
            # Get sheet ID for the specific sheet
            spreadsheet = self.service.spreadsheets().get(
                spreadsheetId=self.spreadsheet_id
            ).execute()

            sheet_id = None
            for sheet in spreadsheet['sheets']:
                if sheet['properties']['title'] == sheet_name:
                    sheet_id = sheet['properties']['sheetId']
                    break

            if sheet_id is None:
                logging.error(f"Sheet '{sheet_name}' not found for formatting")
                return

            # Prepare formatting requests
            requests = []

            # 1. Format Column A (Date) - Date/Time format
            requests.append({
                'repeatCell': {
                    'range': {
                        'sheetId': sheet_id,
                        'startRowIndex': 1,  # Skip header
                        'endRowIndex': num_rows + 1,
                        'startColumnIndex': 0,  # Column A
                        'endColumnIndex': 1
                    },
                    'cell': {
                        'userEnteredFormat': {
                            'numberFormat': {
                                'type': 'DATE_TIME',
                                'pattern': 'yyyy-mm-dd hh:mm:ss'
                            }
                        }
                    },
                    'fields': 'userEnteredFormat.numberFormat'
                }
            })

            # 2. Format Column B (Exchange Rate) - Number with 6 decimal places
            requests.append({
                'repeatCell': {
                    'range': {
                        'sheetId': sheet_id,
                        'startRowIndex': 1,
                        'endRowIndex': num_rows + 1,
                        'startColumnIndex': 1,  # Column B
                        'endColumnIndex': 2
                    },
                    'cell': {
                        'userEnteredFormat': {
                            'numberFormat': {
                                'type': 'NUMBER',
                                'pattern': '#,##0.000000'
                            }
                        }
                    },
                    'fields': 'userEnteredFormat.numberFormat'
                }
            })

            # 3. Format Column C (COP Amount) - Currency COP, no decimals
            requests.append({
                'repeatCell': {
                    'range': {
                        'sheetId': sheet_id,
                        'startRowIndex': 1,
                        'endRowIndex': num_rows + 1,
                        'startColumnIndex': 2,  # Column C
                        'endColumnIndex': 3
                    },
                    'cell': {
                        'userEnteredFormat': {
                            'numberFormat': {
                                'type': 'CURRENCY',
                                'pattern': '"$"#,##0" COP"'
                            }
                        }
                    },
                    'fields': 'userEnteredFormat.numberFormat'
                }
            })

            # 4. Format Column D (USD Amount) - Currency USD, 4 decimal places
            requests.append({
                'repeatCell': {
                    'range': {
                        'sheetId': sheet_id,
                        'startRowIndex': 1,
                        'endRowIndex': num_rows + 1,
                        'startColumnIndex': 3,  # Column D
                        'endColumnIndex': 4
                    },
                    'cell': {
                        'userEnteredFormat': {
                            'numberFormat': {
                                'type': 'CURRENCY',
                                'pattern': '"$"#,##0.0000" USD"'
                            }
                        }
                    },
                    'fields': 'userEnteredFormat.numberFormat'
                }
            })

            # 5. Format Columns E, F, G, H (Text columns) - Plain text with wrapping
            for col_index in range(4, 8):  # Columns E through H
                requests.append({
                    'repeatCell': {
                        'range': {
                            'sheetId': sheet_id,
                            'startRowIndex': 1,
                            'endRowIndex': num_rows + 1,
                            'startColumnIndex': col_index,
                            'endColumnIndex': col_index + 1
                        },
                        'cell': {
                            'userEnteredFormat': {
                                'wrapStrategy': 'WRAP',
                                'verticalAlignment': 'TOP'
                            }
                        },
                        'fields': 'userEnteredFormat.wrapStrategy,userEnteredFormat.verticalAlignment'
                    }
                })

            # Apply all formatting requests
            if requests:
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': requests}
                ).execute()

                logging.info(f"Applied column formatting to {sheet_name}")

        except Exception as e:
            logging.error(f"Error applying column formatting: {e}")
            raise

    def apply_data_validation(self, sheet_name, num_rows):
        """
        Apply data validation dropdowns for category columns.

        :param sheet_name: Name of the sheet to apply validation to
        :param num_rows: Number of data rows (excluding header)
        """
        try:
            # Get sheet ID
            spreadsheet = self.service.spreadsheets().get(
                spreadsheetId=self.spreadsheet_id
            ).execute()

            sheet_id = None
            for sheet in spreadsheet['sheets']:
                if sheet['properties']['title'] == sheet_name:
                    sheet_id = sheet['properties']['sheetId']
                    break

            if sheet_id is None:
                logging.error(f"Sheet '{sheet_name}' not found for validation")
                return

            # Main categories for Column G validation
            main_categories = [
                "Ingresos",
                "Gastos Esenciales",
                "Gastos Discrecionales",
                "Pago de Deudas",
                "Ahorros",
                "Inversiones"
            ]

            requests = []

            # Data validation for Column G (Categoría Principal)
            requests.append({
                'setDataValidation': {
                    'range': {
                        'sheetId': sheet_id,
                        'startRowIndex': 1,
                        'endRowIndex': num_rows + 1,
                        'startColumnIndex': 6,  # Column G
                        'endColumnIndex': 7
                    },
                    'rule': {
                        'condition': {
                            'type': 'ONE_OF_LIST',
                            'values': [{'userEnteredValue': cat} for cat in main_categories]
                        },
                        'showCustomUi': True,
                        'strict': False
                    }
                }
            })

            # Apply validation requests
            if requests:
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': requests}
                ).execute()

                logging.info(f"Applied data validation to {sheet_name}")

        except Exception as e:
            logging.error(f"Error applying data validation: {e}")
            raise

    def apply_header_formatting(self, sheet_name):
        """
        Apply special formatting to the header row.

        :param sheet_name: Name of the sheet to format headers
        """
        try:
            # Get sheet ID
            spreadsheet = self.service.spreadsheets().get(
                spreadsheetId=self.spreadsheet_id
            ).execute()

            sheet_id = None
            for sheet in spreadsheet['sheets']:
                if sheet['properties']['title'] == sheet_name:
                    sheet_id = sheet['properties']['sheetId']
                    break

            if sheet_id is None:
                logging.error(f"Sheet '{sheet_name}' not found for header formatting")
                return

            requests = []

            # Format header row (row 1)
            requests.append({
                'repeatCell': {
                    'range': {
                        'sheetId': sheet_id,
                        'startRowIndex': 0,  # Header row
                        'endRowIndex': 1,
                        'startColumnIndex': 0,  # All columns
                        'endColumnIndex': 8
                    },
                    'cell': {
                        'userEnteredFormat': {
                            'backgroundColor': {
                                'red': 0.9,
                                'green': 0.9,
                                'blue': 0.9
                            },
                            'textFormat': {
                                'bold': True,
                                'fontSize': 11
                            },
                            'horizontalAlignment': 'CENTER',
                            'verticalAlignment': 'MIDDLE'
                        }
                    },
                    'fields': 'userEnteredFormat'
                }
            })

            # Freeze header row
            requests.append({
                'updateSheetProperties': {
                    'properties': {
                        'sheetId': sheet_id,
                        'gridProperties': {
                            'frozenRowCount': 1
                        }
                    },
                    'fields': 'gridProperties.frozenRowCount'
                }
            })

            # Auto-resize columns
            requests.append({
                'autoResizeDimensions': {
                    'dimensions': {
                        'sheetId': sheet_id,
                        'dimension': 'COLUMNS',
                        'startIndex': 0,
                        'endIndex': 8
                    }
                }
            })

            # Apply header formatting
            if requests:
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': requests}
                ).execute()

                logging.info(f"Applied header formatting to {sheet_name}")

        except Exception as e:
            logging.error(f"Error applying header formatting: {e}")
            raise

    def create_subcategory_validation_sheet(self):
        """
        Create a helper sheet with subcategory data for advanced validation.
        This enables conditional dropdowns for subcategories based on main category.
        """
        try:
            validation_sheet_name = "Category_Validation_Data"

            # Check if validation sheet already exists
            spreadsheet = self.service.spreadsheets().get(
                spreadsheetId=self.spreadsheet_id
            ).execute()

            existing_sheets = [sheet['properties']['title'] for sheet in spreadsheet['sheets']]

            if validation_sheet_name not in existing_sheets:
                # Create the validation sheet
                self.create_sheet_tab(validation_sheet_name)

                # Prepare subcategory data organized by main category
                subcategory_data = [
                    ["Main Category", "Subcategories"],
                    ["Ingresos", "Salario, Freelance - Clau, Freelance - Euros, Ventas, Fondos Tri, Ventas Crypto, Pago Prestamos, Otros"],
                    ["Gastos Esenciales", "Mercado, Gym, Samson, Salud, Prepagada, Admin Qz, Servicios, Casa Internet, Admin Qz Extra, Casa Servicios"],
                    ["Gastos Discrecionales", "Domicilio, Comida Rapida, Subs DFY, Subs FUN, Banco Mango, Scotis Seguros, Estudio o Libros, Paseo, Regalo, Ropa, Uber, Carro Gasolina, Carro Parqueadero, Carro Gastos, Casa Mercado, Casa Varios, Casa Limpieza, Hobbies, Nomada"],
                    ["Pago de Deudas", "Prestamo Clau, Tarjeta Cr, Deudas Oz, Deudas Santa Marta"],
                    ["Ahorros", "Pago Futuros, Colchon, USD"],
                    ["Inversiones", "Cripto, Acciones, Otros, ETF"]
                ]

                # Write subcategory data to validation sheet
                validation_range = f"{validation_sheet_name}!A:B"
                temp_client = GoogleSheetsClient(
                    spreadsheet_id=self.spreadsheet_id,
                    credentials_file=self.credentials_file,
                    sheet_range=validation_range
                )
                temp_client.batch_update_rows(subcategory_data)

                # Hide the validation sheet
                sheet_id = None
                spreadsheet = self.service.spreadsheets().get(
                    spreadsheetId=self.spreadsheet_id
                ).execute()

                for sheet in spreadsheet['sheets']:
                    if sheet['properties']['title'] == validation_sheet_name:
                        sheet_id = sheet['properties']['sheetId']
                        break

                if sheet_id:
                    self.service.spreadsheets().batchUpdate(
                        spreadsheetId=self.spreadsheet_id,
                        body={
                            'requests': [{
                                'updateSheetProperties': {
                                    'properties': {
                                        'sheetId': sheet_id,
                                        'hidden': True
                                    },
                                    'fields': 'hidden'
                                }
                            }]
                        }
                    ).execute()

                logging.info(f"Created and populated validation sheet: {validation_sheet_name}")

        except Exception as e:
            logging.error(f"Error creating subcategory validation sheet: {e}")
            # Don't raise - this is optional functionality

    def apply_advanced_subcategory_validation(self, sheet_name, num_rows):
        """
        Apply conditional data validation for subcategories based on main category.
        This creates a more sophisticated dropdown that changes based on the main category selection.

        :param sheet_name: Name of the sheet to apply validation to
        :param num_rows: Number of data rows (excluding header)
        """
        try:
            # First ensure the validation sheet exists
            self.create_subcategory_validation_sheet()

            # Get sheet ID
            spreadsheet = self.service.spreadsheets().get(
                spreadsheetId=self.spreadsheet_id
            ).execute()

            sheet_id = None
            for sheet in spreadsheet['sheets']:
                if sheet['properties']['title'] == sheet_name:
                    sheet_id = sheet['properties']['sheetId']
                    break

            if sheet_id is None:
                logging.error(f"Sheet '{sheet_name}' not found for advanced validation")
                return

            # For now, we'll apply a comprehensive list of all subcategories
            # Google Sheets conditional validation requires more complex setup
            all_subcategories = [
                # Ingresos
                "Salario", "Freelance - Clau", "Freelance - Euros", "Ventas", "Fondos Tri", "Ventas Crypto", "Pago Prestamos", "Otros",
                # Gastos Esenciales
                "Mercado", "Gym", "Samson", "Salud", "Prepagada", "Admin Qz", "Servicios", "Casa Internet", "Admin Qz Extra", "Casa Servicios",
                # Gastos Discrecionales
                "Domicilio", "Comida Rapida", "Subs DFY", "Subs FUN", "Banco Mango", "Scotis Seguros", "Estudio o Libros", "Paseo", "Regalo", "Ropa", "Uber", "Carro Gasolina", "Carro Parqueadero", "Carro Gastos", "Casa Mercado", "Casa Varios", "Casa Limpieza", "Hobbies", "Nomada",
                # Pago de Deudas
                "Prestamo Clau", "Tarjeta Cr", "Deudas Oz", "Deudas Santa Marta",
                # Ahorros
                "Pago Futuros", "Colchon", "USD",
                # Inversiones
                "Cripto", "Acciones", "Otros", "ETF"
            ]

            requests = []

            # Data validation for Column H (Subcategoría)
            requests.append({
                'setDataValidation': {
                    'range': {
                        'sheetId': sheet_id,
                        'startRowIndex': 1,
                        'endRowIndex': num_rows + 1,
                        'startColumnIndex': 7,  # Column H
                        'endColumnIndex': 8
                    },
                    'rule': {
                        'condition': {
                            'type': 'ONE_OF_LIST',
                            'values': [{'userEnteredValue': subcat} for subcat in all_subcategories]
                        },
                        'showCustomUi': True,
                        'strict': False
                    }
                }
            })

            # Apply validation
            if requests:
                self.service.spreadsheets().batchUpdate(
                    spreadsheetId=self.spreadsheet_id,
                    body={'requests': requests}
                ).execute()

                logging.info(f"Applied advanced subcategory validation to {sheet_name}")

        except Exception as e:
            logging.error(f"Error applying advanced subcategory validation: {e}")
            # Don't raise - this is optional functionality
