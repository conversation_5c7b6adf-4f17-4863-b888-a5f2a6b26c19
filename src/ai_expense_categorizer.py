import os
import json
import logging
from typing import <PERSON><PERSON>, Dict, Optional
from openai import OpenAI

class AIExpenseCategorizer:
    """
    AI-powered expense categorization system using OpenAI's GPT-4 model.
    Provides intelligent transaction analysis with user profile context and
    existing categorization rules for a Colombian software engineer.
    """
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the AI categorizer with OpenAI API key.
        
        Args:
            api_key: OpenAI API key. If None, reads from OPENAI_API_KEY environment variable.
        """
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            raise ValueError("OpenAI API key is required. Set OPENAI_API_KEY environment variable or pass api_key parameter.")
        
        self.client = OpenAI(api_key=self.api_key)
        
        # Main categories
        self.MAIN_CATEGORIES = {
            'INGRESOS': 'Ingresos',
            'GASTOS_ESENCIALES': 'Gastos Esenciales',
            'GASTOS_DISCRECIONALES': 'Gastos Discrecionales',
            'PAGO_DEUDAS': 'Pago de <PERSON>',
            'AHORROS': 'Ahorros',
            'INVERSIONES': 'Inversiones'
        }
        
        # Detailed subcategories for AI context
        self.SUBCATEGORIES = {
            'Ingresos': [
                'Salario', 'Freelance - Clau', 'Freelance - Euros', 'Ventas',
                'Fondos Tri', 'Ventas Crypto', 'Pago Prestamos', 'Otros'
            ],
            'Gastos Esenciales': [
                'Mercado', 'Samson', 'Salud', 'Servicios', 'Casa Internet',
                'Casa Celular', 'Gym', 'Otros'
            ],
            'Gastos Discrecionales': [
                'Domicilio', 'Comida Rapida', 'Subs DFY', 'Subs FUN', 'Uber',
                'Carro Gasolina', 'Carro Mantenimiento', 'Ropa', 'Estudio o Libros',
                'Viajes', 'Otros'
            ],
            'Pago de Deudas': [
                'Tarjeta Cr', 'Prestamo Clau', 'Deudas Oz', 'Deudas Santa Marta', 'Otros'
            ],
            'Ahorros': [
                'Pago Futuros', 'Colchon', 'USD', 'Otros'
            ],
            'Inversiones': [
                'Cripto', 'Acciones', 'ETF', 'Otros'
            ]
        }
        
        # User profile context for better categorization
        self.USER_PROFILE = {
            'age': 36,
            'profession': 'Software Engineer',
            'location': 'Medellín, Colombia',
            'interests': ['Technology', 'Programming', 'Online Shopping'],
            'common_merchants': [
                'Amazon', 'eBay', 'BuscaLibre', 'MercadoLibre', 'Rappi',
                'Mundo Verde', 'UNE Telecom', 'EDS Premium', 'Bancolombia'
            ],
            'payment_methods': ['Bancolombia', 'Credit Cards', 'QR transfers'],
            'lifestyle': 'Tech enthusiast, car owner, food delivery user'
        }
        
        # Configure logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def categorize_expense(self, place: str, amount: str, description: str = "") -> Tuple[str, str, float]:
        """
        Categorize an expense using AI analysis.
        
        Args:
            place: The merchant/place name
            amount: Transaction amount
            description: Full transaction description
            
        Returns:
            Tuple[str, str, float]: (main_category, subcategory, confidence_score)
        """
        try:
            # Prepare the prompt for GPT-4
            prompt = self._build_categorization_prompt(place, amount, description)
            
            # Call OpenAI API
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,  # Low temperature for consistent categorization
                max_tokens=200
            )
            
            # Parse the response
            result = self._parse_ai_response(response.choices[0].message.content)
            
            if result:
                main_cat, sub_cat, confidence = result
                self.logger.info(f"AI categorized '{place}' as {main_cat} -> {sub_cat} (confidence: {confidence:.2f})")
                return main_cat, sub_cat, confidence
            else:
                self.logger.warning(f"Failed to parse AI response for '{place}', using fallback")
                return self._fallback_categorization(place, description)
                
        except Exception as e:
            self.logger.error(f"AI categorization failed for '{place}': {e}")
            return self._fallback_categorization(place, description)

    def _get_system_prompt(self) -> str:
        """Get the system prompt that defines the AI's role and context."""
        return f"""You are an expert expense categorization assistant for a {self.USER_PROFILE['age']}-year-old {self.USER_PROFILE['profession']} living in {self.USER_PROFILE['location']}.

USER PROFILE:
- Profession: {self.USER_PROFILE['profession']}
- Location: {self.USER_PROFILE['location']}
- Interests: {', '.join(self.USER_PROFILE['interests'])}
- Lifestyle: {self.USER_PROFILE['lifestyle']}
- Common merchants: {', '.join(self.USER_PROFILE['common_merchants'])}

CATEGORIZATION SYSTEM:
You must categorize expenses into exactly these 6 main categories and their subcategories:

1. Ingresos: {', '.join(self.SUBCATEGORIES['Ingresos'])}
2. Gastos Esenciales: {', '.join(self.SUBCATEGORIES['Gastos Esenciales'])}
3. Gastos Discrecionales: {', '.join(self.SUBCATEGORIES['Gastos Discrecionales'])}
4. Pago de Deudas: {', '.join(self.SUBCATEGORIES['Pago de Deudas'])}
5. Ahorros: {', '.join(self.SUBCATEGORIES['Ahorros'])}
6. Inversiones: {', '.join(self.SUBCATEGORIES['Inversiones'])}

SPECIFIC RULES:
- Mundo Verde → Gastos Esenciales > Mercado
- Rappi → Gastos Discrecionales > Domicilio
- UNE Telecom → Gastos Esenciales > Casa Internet
- EDS Premium → Gastos Discrecionales > Carro Gasolina
- Bancolombia transfers → Context-dependent (usually Gastos Discrecionales > Otros)
- USD transfers to Elena → Pago de Deudas > Deudas Oz
- USD transfers to Carlos → Pago de Deudas > Deudas Santa Marta
- Tech purchases (Amazon, eBay, MercadoLibre) → Usually Gastos Discrecionales > Otros
- Food delivery → Gastos Discrecionales > Domicilio
- Gas stations → Gastos Discrecionales > Carro Gasolina

RESPONSE FORMAT:
Always respond with exactly this JSON format:
{{"main_category": "Category Name", "subcategory": "Subcategory Name", "confidence": 0.95, "reasoning": "Brief explanation"}}

Be precise and consistent. Consider Colombian context and the user's tech background."""

    def _build_categorization_prompt(self, place: str, amount: str, description: str) -> str:
        """Build the user prompt for categorization."""
        return f"""Categorize this transaction:

MERCHANT/PLACE: {place}
AMOUNT: {amount}
DESCRIPTION: {description}

Analyze this transaction considering:
1. The merchant name and type of business
2. The transaction description and context
3. The user's profile as a Colombian software engineer
4. The specific categorization rules provided
5. Common spending patterns for this user type

Provide your categorization with confidence score (0.0-1.0) and brief reasoning."""

    def _parse_ai_response(self, response_text: str) -> Optional[Tuple[str, str, float]]:
        """Parse the AI response and extract categorization data."""
        try:
            # Clean the response text
            response_text = response_text.strip()
            
            # Try to find JSON in the response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx > start_idx:
                json_str = response_text[start_idx:end_idx]
                result = json.loads(json_str)
                
                main_cat = result.get('main_category', '').strip()
                sub_cat = result.get('subcategory', '').strip()
                confidence = float(result.get('confidence', 0.0))
                
                # Validate categories
                if main_cat in self.SUBCATEGORIES and sub_cat in self.SUBCATEGORIES[main_cat]:
                    return main_cat, sub_cat, confidence
                else:
                    self.logger.warning(f"Invalid categories from AI: {main_cat} -> {sub_cat}")
                    return None
            else:
                self.logger.warning("No valid JSON found in AI response")
                return None
                
        except (json.JSONDecodeError, ValueError, KeyError) as e:
            self.logger.error(f"Failed to parse AI response: {e}")
            return None

    def _fallback_categorization(self, place: str, description: str) -> Tuple[str, str, float]:
        """Fallback categorization when AI fails."""
        # Simple rule-based fallback
        place_lower = place.lower()
        desc_lower = description.lower()
        
        # Basic categorization rules
        if any(keyword in place_lower for keyword in ['rappi', 'domicilio']):
            return 'Gastos Discrecionales', 'Domicilio', 0.7
        elif any(keyword in place_lower for keyword in ['mundo verde', 'exito', 'carulla']):
            return 'Gastos Esenciales', 'Mercado', 0.7
        elif any(keyword in place_lower for keyword in ['netflix', 'spotify', 'amazon']):
            return 'Gastos Discrecionales', 'Subs DFY', 0.7
        elif any(keyword in place_lower for keyword in ['uber', 'taxi']):
            return 'Gastos Discrecionales', 'Uber', 0.7
        elif any(keyword in place_lower for keyword in ['terpel', 'esso', 'eds']):
            return 'Gastos Discrecionales', 'Carro Gasolina', 0.7
        else:
            return 'Gastos Discrecionales', 'Otros', 0.5

    def get_all_categories(self) -> Dict[str, list]:
        """Get all available categories and subcategories."""
        return self.SUBCATEGORIES.copy()

    def validate_api_key(self) -> bool:
        """Validate that the OpenAI API key is working."""
        try:
            response = self.client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=5
            )
            return True
        except Exception as e:
            self.logger.error(f"API key validation failed: {e}")
            return False
