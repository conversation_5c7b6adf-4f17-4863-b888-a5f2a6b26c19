help:
	@egrep -h '\s##\s' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m  %-30s\033[0m %s\n", $$1, $$2}'

prepare: ## How to prepare environment
	uv venv myenv

install: ## Install dependencies
	uv pip install -r requirements.txt

run: ## Run sample transaction
	python3 src/run.py --sample

test: ## Run unit tests
	python3 -m unittest discover -s tests

# Migration commands
migrate-dry-run: ## Preview CSV migration without writing to Google Sheets (with AI)
	python3 src/run.py --migrate-csv expenses.csv --dry-run

migrate: ## Migrate CSV data to Google Sheets with AI categorization
	python3 src/run.py --migrate-csv expenses.csv

migrate-verbose: ## Migrate with detailed logging (with AI)
	python3 src/run.py --migrate-csv expenses.csv --dry-run --verbose

migrate-custom: ## Migrate with custom sheet name (usage: make migrate-custom SHEET_NAME="My Custom Name")
	python3 src/run.py --migrate-csv expenses.csv --sheet-name "$(SHEET_NAME)"

migrate-no-ai: ## Migrate using only rule-based categorization (no AI)
	python3 src/run.py --migrate-csv expenses.csv --no-ai

migrate-no-ai-dry-run: ## Preview migration with rule-based categorization only
	python3 src/run.py --migrate-csv expenses.csv --no-ai --dry-run

migrate-help: ## Show migration command help
	python3 src/run.py --help

# Validation and demonstration commands
demo: ## Run expense categorization demonstration
	@echo "🎯 EXPENSE CATEGORIZATION DEMONSTRATION"
	@echo "========================================"
	@python3 -c "import sys; sys.path.append('src'); from expense_categorizer import ExpenseCategorizer; cat = ExpenseCategorizer(); print('✅ Testing Rappi transaction:'); result = cat.categorize_expense('Rappi Supermercado', '33450', 'Compra mercado'); print(f'   Category: {result[0]} → {result[1]}'); print('✅ Testing Netflix transaction:'); result = cat.categorize_expense('Netflix', '50000', 'Suscripción mensual'); print(f'   Category: {result[0]} → {result[1]}'); print('✅ All categorization working correctly!')"

validate: ## Run comprehensive validation of the expense system
	@echo "🧪 RUNNING COMPREHENSIVE VALIDATION"
	@echo "===================================="
	@python3 -c "import sys; sys.path.append('src'); from expense_info import ExpenseInfo; print('✅ Testing problematic transaction parsing:'); result = ExpenseInfo.parse('scotiabank colpatria: realizaste transaccion en rappi*rappi colombia por 33,450 con tu tarjeta visa platinum 2025/06/19 12:20:15'); print(f'   Parsed: {result.amount} {result.currency} at {result.place}' if result else '❌ Failed to parse'); print('✅ Validation complete!')"

# Development commands
lint: ## Check code style (if flake8 is installed)
	@command -v flake8 >/dev/null 2>&1 && flake8 src/ tests/ || echo "flake8 not installed, skipping lint"

clean: ## Clean up temporary files
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete

# AI Categorization commands
ai-demo: ## Run AI categorization demonstration (requires OPENAI_API_KEY)
	@echo "🤖 AI EXPENSE CATEGORIZATION DEMONSTRATION"
	@echo "=========================================="
	@python3 -c "import sys, os; sys.path.append('src'); from ai_expense_categorizer import AIExpenseCategorizer; print('✅ Testing AI categorization:'); cat = AIExpenseCategorizer(); result = cat.categorize_expense('Rappi', '45000', 'Pedido almuerzo'); print(f'   AI Result: {result[0]} → {result[1]} (confidence: {result[2]:.2f})'); result = cat.categorize_expense('Amazon', '250', 'Mechanical keyboard'); print(f'   AI Result: {result[0]} → {result[1]} (confidence: {result[2]:.2f})'); print('✅ AI categorization working correctly!')"

ai-validate: ## Validate AI categorization system (requires OPENAI_API_KEY)
	@echo "🧪 VALIDATING AI CATEGORIZATION SYSTEM"
	@echo "======================================"
	@python3 -c "import sys, os; sys.path.append('src'); from ai_expense_categorizer import AIExpenseCategorizer; cat = AIExpenseCategorizer(); print('✅ API Key validation:', cat.validate_api_key()); print('✅ Categories available:', len(cat.get_all_categories())); print('✅ AI validation complete!')"

ai-test: ## Run AI categorization tests
	python3 -m unittest tests.test_ai_expense_categorizer -v

ai-integration-test: ## Run integration tests with AI categorization
	python3 -m unittest tests.test_push_notification_processor.TestPushNotificationProcessor.test_ai_categorization_enabled -v

ai-fallback-test: ## Test AI categorization fallback scenarios
	python3 -m unittest tests.test_push_notification_processor.TestPushNotificationProcessor.test_ai_categorization_fallback_on_error -v

ai-compare: ## Compare AI vs rule-based categorization for sample transactions
	@echo "🔍 COMPARING AI VS RULE-BASED CATEGORIZATION"
	@echo "============================================"
	@python3 -c "import sys, os; sys.path.append('src'); from ai_expense_categorizer import AIExpenseCategorizer; from expense_categorizer import ExpenseCategorizer; ai_cat = AIExpenseCategorizer(); rule_cat = ExpenseCategorizer(); test_cases = [('Rappi', '45000', 'Pedido almuerzo'), ('Amazon', '250', 'Mechanical keyboard'), ('Netflix', '50000', 'Suscripción mensual')]; print('Transaction | AI Result | Rule-based Result'); print('-' * 60); [print(f'{place:12} | {ai_cat.categorize_expense(place, amount, desc)[0]:15} → {ai_cat.categorize_expense(place, amount, desc)[1]:12} | {rule_cat.categorize_expense(place, amount, desc)[0]:15} → {rule_cat.categorize_expense(place, amount, desc)[1]}') for place, amount, desc in test_cases]"

# Quick start commands
setup: prepare install ## Complete setup: create venv and install dependencies

check: test validate ## Run all tests and validation

ai-check: ai-test ai-validate ## Run all AI categorization tests and validation
